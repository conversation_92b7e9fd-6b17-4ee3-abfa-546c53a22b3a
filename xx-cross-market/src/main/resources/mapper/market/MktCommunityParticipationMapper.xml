<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.market.mapper.MktCommunityParticipationMapper">

    <!-- 统计用户在指定活动的成功参与次数 -->
    <select id="countSuccessParticipation" resultType="long">
        SELECT COUNT(1)
        FROM mkt_community_participation
        WHERE activity_id = #{activityId}
          AND passenger_id = #{passengerId}
          AND status = '1'
          AND del_flag = '0'
    </select>

    <!-- 统计用户在指定活动指定时间范围内的成功参与次数 -->
    <select id="countSuccessParticipationByTimeRange" resultType="long">
        SELECT COUNT(1)
        FROM mkt_community_participation
        WHERE activity_id = #{activityId}
          AND passenger_id = #{passengerId}
          AND status = '1'
          AND del_flag = '0'
          AND receive_time >= #{startTime}
          AND receive_time &lt; #{endTime}
    </select>

</mapper>

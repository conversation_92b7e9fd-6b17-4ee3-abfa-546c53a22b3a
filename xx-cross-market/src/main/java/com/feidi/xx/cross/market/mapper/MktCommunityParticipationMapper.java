package com.feidi.xx.cross.market.mapper;

import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.market.domain.MktCommunityParticipation;
import com.feidi.xx.cross.market.domain.vo.MktCommunityParticipationVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 活动领取记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface MktCommunityParticipationMapper extends BaseMapperPlus<MktCommunityParticipation, MktCommunityParticipationVo> {

    /**
     * 统计用户在指定活动的成功参与次数
     *
     * @param activityId 活动ID
     * @param passengerId 用户ID
     * @return 成功参与次数
     */
    long countSuccessParticipation(@Param("activityId") Long activityId, @Param("passengerId") Long passengerId);

    /**
     * 统计用户在指定活动指定时间范围内的成功参与次数
     *
     * @param activityId 活动ID
     * @param passengerId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功参与次数
     */
    long countSuccessParticipationByTimeRange(@Param("activityId") Long activityId,
                                              @Param("passengerId") Long passengerId,
                                              @Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime);
}

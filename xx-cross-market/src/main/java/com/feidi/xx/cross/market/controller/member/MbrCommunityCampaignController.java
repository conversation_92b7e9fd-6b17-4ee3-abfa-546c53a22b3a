package com.feidi.xx.cross.market.controller.member;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.cross.market.service.IMktCommunityCampaignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 乘客端-社群活动
 * 前端访问路由地址为:/market/mbr/communityCampaign
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX + "/communityCampaign")
public class MbrCommunityCampaignController {
    private final IMktCommunityCampaignService mktCommunityCampaignService;
    //TODO R 查询活动以及活动卡券（如果已领取，则展示已领取卡券信息）
    /**
     * 领取卡券
     * @param id 活动ID
     */
    @PostMapping("/receiveCoupon/{id}")
    public R<Void> receiveCoupon(Long id) {
        Assert.notNull(id, "活动ID不能为空");
        mktCommunityCampaignService.receiveCoupon(id);
        return R.ok();
    }
}

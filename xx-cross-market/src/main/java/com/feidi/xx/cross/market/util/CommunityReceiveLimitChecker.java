package com.feidi.xx.cross.market.util;

import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.cross.common.enums.market.ReceiveLimitTypeEnum;
import com.feidi.xx.cross.market.domain.MktCommunityCampaign;
import com.feidi.xx.cross.market.mapper.MktCommunityParticipationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 社群活动领取限制检查工具类
 * 根据活动配置的领取限制类型和用户的参与记录进行判断
 *
 * <AUTHOR>
 * @date 2025-09-05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CommunityReceiveLimitChecker {

    private final MktCommunityParticipationMapper communityParticipationMapper;

    /**
     * 检查用户是否可以领取优惠券
     *
     * @param campaign 社群活动信息
     * @param userId   用户ID
     * @return true-可以领取，false-不能领取
     */
    public boolean canReceive(MktCommunityCampaign campaign, Long userId) {
        if (campaign == null || userId == null) {
            log.warn("参数不能为空: campaign={}, userId={}", campaign, userId);
            return false;
        }

        String receiveLimitType = campaign.getReceiveLimitType();
        Long activityId = campaign.getId();

        log.debug("检查用户领取限制: activityId={}, userId={}, receiveLimitType={}", 
                 activityId, userId, receiveLimitType);

        if (ReceiveLimitTypeEnum.ONCE_PER_CAMPAIGN.getCode().equals(receiveLimitType)) {
            return checkOncePer Campaign(activityId, userId);
        } else if (ReceiveLimitTypeEnum.ONCE_PER_DAY.getCode().equals(receiveLimitType)) {
            return checkOncePerDay(activityId, userId);
        }

        // 未知的限制类型，默认不允许领取
        log.warn("未知的领取限制类型: {}", receiveLimitType);
        return false;
    }

    /**
     * 检查活动期间限领一次
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return true-可以领取，false-不能领取
     */
    private boolean checkOncePer Campaign(Long activityId, Long userId) {
        long count = communityParticipationMapper.countSuccessParticipation(activityId, userId);
        boolean canReceive = count == 0;
        
        log.debug("活动期间限领一次检查结果: activityId={}, userId={}, count={}, canReceive={}", 
                 activityId, userId, count, canReceive);
        
        return canReceive;
    }

    /**
     * 检查每天限领一次
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return true-可以领取，false-不能领取
     */
    private boolean checkOncePerDay(Long activityId, Long userId) {
        // 获取今天的开始和结束时间
        Date today = DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date()));
        Date tomorrow = DateUtils.addDays(today, 1);

        long todayCount = communityParticipationMapper.countSuccessParticipationByTimeRange(
            activityId, userId, today, tomorrow);
        
        boolean canReceive = todayCount == 0;
        
        log.debug("每天限领一次检查结果: activityId={}, userId={}, today={}, todayCount={}, canReceive={}", 
                 activityId, userId, today, todayCount, canReceive);
        
        return canReceive;
    }

    /**
     * 获取用户在活动中的总参与次数
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return 总参与次数
     */
    public long getTotalParticipationCount(Long activityId, Long userId) {
        if (activityId == null || userId == null) {
            return 0;
        }
        return communityParticipationMapper.countSuccessParticipation(activityId, userId);
    }

    /**
     * 获取用户今天在活动中的参与次数
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return 今天的参与次数
     */
    public long getTodayParticipationCount(Long activityId, Long userId) {
        if (activityId == null || userId == null) {
            return 0;
        }
        
        Date today = DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, new Date()));
        Date tomorrow = DateUtils.addDays(today, 1);
        
        return communityParticipationMapper.countSuccessParticipationByTimeRange(
            activityId, userId, today, tomorrow);
    }
}

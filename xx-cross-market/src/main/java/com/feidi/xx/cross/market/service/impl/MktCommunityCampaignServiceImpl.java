package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.market.CampaignStatusEnum;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.market.UserCouponSourceEnum;
import com.feidi.xx.cross.market.domain.MktCommunityCampaign;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.bo.*;
import com.feidi.xx.cross.market.domain.vo.MktCommunityCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktCommunityDataRecordVo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.mapper.MktCommunityCampaignMapper;
import com.feidi.xx.cross.market.mapper.MktCouponGrantMapper;
import com.feidi.xx.cross.market.mapper.MktCouponMapper;
import com.feidi.xx.cross.market.service.IMktCommunityCampaignService;
import com.feidi.xx.cross.market.service.IMktCommunityParticipationService;
import com.feidi.xx.cross.market.service.IMktCouponService;
import com.feidi.xx.resource.api.RemoteWxXcxApiService;
import com.feidi.xx.resource.api.domain.RemoteFile;
import com.feidi.xx.resource.api.domain.RemoteWxacodeParamBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.feidi.xx.cross.common.cache.market.constants.MarketCacheConstants.COMMUNITY_QRCODE_KEY;

/**
 * 社群活动主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MktCommunityCampaignServiceImpl implements IMktCommunityCampaignService {

    private final MktCommunityCampaignMapper baseMapper;
    private final MktCouponMapper mktCouponMapper;
    private final IMktCouponService mktCouponService;
    private final MktCouponGrantMapper mktCouponGrantMapper;
    private final IMktCommunityParticipationService communityParticipationService;

    @DubboReference
    private RemoteWxXcxApiService remoteWxXcxApiService;

    /**
     * 查询社群活动主
     *
     * @param id 主键
     * @return 社群活动主
     */
    @Override
    public MktCommunityCampaignVo queryById(Long id) {
        MktCommunityCampaignVo campaignVo = baseMapper.selectVoById(id);
        if (campaignVo == null) {
            return null;
        }

        // 领取数量
        var countBos = mktCouponGrantMapper.selectCouponGrantCountByActivityIds(List.of(campaignVo.getId()), UserCouponSourceEnum.COMMUNITY_ACTIVITY);
        campaignVo.setCouponReceiveCount(countBos.stream()
                .filter(e -> e.getGroupId().equals(campaignVo.getId()))
                .mapToLong(CouponQueryCountBo::getNum).sum());

        // 优惠券信息
        Map<Long, MktCouponVo> couponMap = mktCouponService.queryMapByIds(new HashSet<>(campaignVo.getCouponIds()), true);
        campaignVo.setCouponVos(new ArrayList<>(couponMap.values()));
        campaignVo.setCouponStock(
                couponMap.values().stream().filter(e -> campaignVo.getCouponIds().contains(e.getId()))
                        .mapToInt(MktCouponVo::getMargin).sum());
        // 参与人数
        Map<Long, Long> participationCountMap = communityParticipationService.queryParticipationCountByCampaignIds(Set.of(campaignVo.getId()));
        campaignVo.setParticipationCount(participationCountMap.getOrDefault(campaignVo.getId(), 0L));

        // 使用人数
        var usageStats = mktCouponGrantMapper.selectActivityUsageStats(campaignVo.getId(), UserCouponSourceEnum.COMMUNITY_ACTIVITY, CouponStatusEnum.USED);
        campaignVo.setUseCount(usageStats.stream()
                .filter(e -> e.getGroupId().equals(campaignVo.getId()))
                .mapToLong(TwoNumQueryCountBo::getNum).sum());
        // 优惠券使用数量
        campaignVo.setCouponUseCount(usageStats.stream()
                .filter(e -> e.getGroupId().equals(campaignVo.getId()))
                .mapToLong(TwoNumQueryCountBo::getNum2).sum());

        return campaignVo;
    }

    /**
     * 分页查询社群活动主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 社群活动主分页列表
     */
    @Override
    public TableDataInfo<MktCommunityCampaignVo> queryPageList(MktCommunityCampaignBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktCommunityCampaign> lqw = buildQueryWrapper(bo);
        Page<MktCommunityCampaignVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (CollUtil.isEmpty(result.getRecords())) {
            return TableDataInfo.build(result);
        }

        //优惠券信息
        Set<Long> couponIds = result.getRecords().stream().map(MktCommunityCampaignVo::getCouponIds).flatMap(Collection::stream).collect(Collectors.toSet());
        Map<Long, MktCouponVo> couponMap = mktCouponService.queryMapByIds(couponIds);
        //领取信息
        List<Long> ids = StreamUtils.toList(result.getRecords(), MktCommunityCampaignVo::getId);
        var countBos = mktCouponGrantMapper.selectCouponGrantCountByActivityIds(ids, UserCouponSourceEnum.COMMUNITY_ACTIVITY);
        Map<Long, Long> participationCountMap = communityParticipationService.queryParticipationCountByCampaignIds(new HashSet<>(ids));
        for (MktCommunityCampaignVo campaignVo : result.getRecords()) {
            // 优惠券库存
            campaignVo.setCouponStock(
                    couponMap.values().stream().filter(e -> campaignVo.getCouponIds().contains(e.getId()))
                            .mapToInt(MktCouponVo::getMargin).sum());
            // 领取数量
            campaignVo.setCouponReceiveCount(countBos.stream().filter(e -> e.getGroupId().equals(campaignVo.getId())).mapToLong(CouponQueryCountBo::getNum).sum());

            // 参与人数
            campaignVo.setParticipationCount(participationCountMap.getOrDefault(campaignVo.getId(), 0L));
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的社群活动主列表
     *
     * @param bo 查询条件
     * @return 社群活动主列表
     */
    @Override
    public List<MktCommunityCampaignVo> queryList(MktCommunityCampaignBo bo) {
        LambdaQueryWrapper<MktCommunityCampaign> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktCommunityCampaign> buildQueryWrapper(MktCommunityCampaignBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktCommunityCampaign> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getActivityName()), MktCommunityCampaign::getActivityName, bo.getActivityName());
        lqw.eq(bo.getStartTime() != null, MktCommunityCampaign::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, MktCommunityCampaign::getEndTime, bo.getEndTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MktCommunityCampaign::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getShareTitle()), MktCommunityCampaign::getShareTitle, bo.getShareTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiveLimitType()), MktCommunityCampaign::getReceiveLimitType, bo.getReceiveLimitType());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleContent()), MktCommunityCampaign::getRuleContent, bo.getRuleContent());

        //时间返回交集
        lqw.and(ObjUtil.isAllNotEmpty(bo.getStartTime(), bo.getEndTime()), wrapper ->
                wrapper.lt(MktCommunityCampaign::getStartTime, bo.getEndTime()).gt(MktCommunityCampaign::getEndTime, bo.getStartTime()));
        lqw.func(bo.getTimeRangeForCreateTime() != null, wrapper ->
                wrapper.between(MktCommunityCampaign::getCreateTime, bo.getTimeRangeForCreateTime().getStartTime(), bo.getTimeRangeForCreateTime().getEndTime())
        );
        return lqw;
    }

    /**
     * 新增社群活动主
     *
     * @param bo 社群活动主
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktCommunityCampaignBo bo) {
        MktCommunityCampaign add = MapstructUtils.convert(bo, MktCommunityCampaign.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改社群活动主
     *
     * @param bo 社群活动主
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktCommunityCampaignBo bo) {
        //仅支持待开始/进行中状态的活动进行编辑
        MktCommunityCampaign mktCommunityCampaign = baseMapper.selectById(bo.getId());
        if (mktCommunityCampaign == null) {
            throw new ServiceException("社群活动不存在");
        }
        if (!CampaignStatusEnum.NOT_STARTED.getCode().equals(mktCommunityCampaign.getStatus())
                && !CampaignStatusEnum.ONGOING.getCode().equals(mktCommunityCampaign.getStatus())) {
            throw new ServiceException("仅支持待开始/进行中状态的活动进行编辑");
        }
        MktCommunityCampaign update = MapstructUtils.convert(bo, MktCommunityCampaign.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktCommunityCampaign entity) {
        List<Long> couponIds = entity.getCouponIds().stream().distinct().toList();
        couponIds.parallelStream().forEach(couponId -> {
            MktCoupon coupon = mktCouponMapper.selectById(couponId);
            if (coupon == null) {
                log.error("优惠券不存在，优惠券ID：{}", couponId);
                throw new ServiceException("优惠券不存在");
            }
            //卡券过期时间两种情况
            if (coupon.getEndTime() != null && coupon.getEndTime().before(entity.getEndTime())) {
                throw new ServiceException(coupon.getName() + "最晚可使用时间不能小于活动结束时间");
            }
        });
    }

    /**
     * 校验并批量删除社群活动主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void updateStatus(MktCommunityCampaignBo bo) {
        MktCommunityCampaign campaign = baseMapper.selectById(bo.getId());
        if (campaign == null) {
            throw new ServiceException("社群活动不存在");
        }
        if (campaign.getStatus().equals(bo.getStatus())) {
            return;
        }
        if (!CampaignStatusEnum.ENDED.getCode().equals(bo.getStatus())) {
            throw new ServiceException("仅支持结束活动");
        }
        LambdaUpdateWrapper<MktCommunityCampaign> updateWrapper = Wrappers.<MktCommunityCampaign>lambdaUpdate()
                .eq(MktCommunityCampaign::getId, bo.getId())
                .set(MktCommunityCampaign::getStatus, bo.getStatus());
        baseMapper.update(updateWrapper);
        log.info("活动状态更新成功，活动ID: {}, 原状态: {}, 新状态: {}, 操作人: {}",
                bo.getId(), campaign.getStatus(), bo.getStatus(), LoginHelper.getUserId());
    }

    @Override
    public String share(Long id, MktCommunityCampaignShareBo bo) {
        String key = COMMUNITY_QRCODE_KEY + id;
        Object object = RedisUtils.getCacheObject(key);
        if (object != null) {
            return (String) object;
        }
        MktCommunityCampaign communityCampaign = baseMapper.selectById(id);
        if (communityCampaign == null) {
            throw new ServiceException("社群活动不存在");
        }
        if (CampaignStatusEnum.ENDED.getCode().equals(communityCampaign.getStatus())) {
            throw new ServiceException("社群活动已结束");
        }
        RemoteWxacodeParamBo remoteWxacodeParamBo = new RemoteWxacodeParamBo();
        //这里写死乘客端APPID
        remoteWxacodeParamBo.setAppId("wxe95b62733930db05");
        remoteWxacodeParamBo.setScene("mccId=" + id);
        remoteWxacodeParamBo.setPage(bo.getPage());
        remoteWxacodeParamBo.setEnvVersion(bo.getEnvVersion());
        RemoteFile getwxacodeunlimit = remoteWxXcxApiService.getwxacodeunlimit(remoteWxacodeParamBo);
        if (getwxacodeunlimit != null) {
            RedisUtils.setCacheObject(key, getwxacodeunlimit.getUrl());
            RedisUtils.expire(key, Duration.ofDays(15));
            return getwxacodeunlimit.getUrl();
        }
        log.error("生成二维码失败，活动ID: {}", id);
        throw new ServiceException("生成二维码失败");
    }

    @Override
    public TableDataInfo<MktCommunityDataRecordVo> queryDataRecordList(MktCommunityDataRecordBo bo, PageQuery pageQuery) {
        Page<MktCommunityDataRecordVo> page = pageQuery.build();
        Page<MktCommunityDataRecordVo> result = baseMapper.selectDataRecordPage(page, bo);
        return TableDataInfo.build(result);
    }

    @Override
    public List<MktCommunityDataRecordVo> queryDataRecordExportList(MktCommunityDataRecordBo bo) {
        // 创建一个大的分页对象来获取所有数据
        Page<MktCommunityDataRecordVo> page = new Page<>(1, 10000);
        Page<MktCommunityDataRecordVo> result = baseMapper.selectDataRecordPage(page, bo);
        return result.getRecords();
    }

    @Override
    public void receiveCoupon(Long id) {
        Long userId = LoginHelper.getUserId();
        MktCommunityCampaign mktCommunityCampaign = baseMapper.selectById(id);
        if (mktCommunityCampaign == null) {
            throw new ServiceException("社群活动不存在");
        }
        if (!CampaignStatusEnum.ONGOING.getCode().equals(mktCommunityCampaign.getStatus())) {
            throw new ServiceException("活动未开始或已结束");
        }
        //根据
    }
}
